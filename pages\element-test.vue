
<script setup lang="ts">
import { reactive } from 'vue'
import { 
  Search, 
  Edit, 
  Check, 
  Message, 
  Star, 
  Delete 
} from '@element-plus/icons-vue'

// 表单数据
const form = reactive({
  username: '',
  password: '',
  gender: '',
  hobbies: []
})

// 表格数据
const tableData = [
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    date: '2016-05-04',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1517 弄'
  },
  {
    date: '2016-05-01',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1519 弄'
  }
]

// 表单提交
const onSubmit = () => {
  console.log('表单数据:', form)
  ElMessage.success('提交成功！')
}

// 表单重置
const onReset = () => {
  form.username = ''
  form.password = ''
  form.gender = ''
  form.hobbies = []
  ElMessage.info('表单已重置')
}

// 显示消息
const showMessage = () => {
  ElMessage({
    message: '这是一条消息提示',
    type: 'success',
  })
}

// 显示通知
const showNotification = () => {
  ElNotification({
    title: '成功',
    message: '这是一条成功的提示消息',
    type: 'success',
  })
}

// 显示警告
const showAlert = () => {
  ElMessageBox.alert('这是一段内容', '标题名称', {
    confirmButtonText: '确定',
  })
}
</script>


<template>
  <div class="element-test-page">
    <h1>Element Plus 自动导入测试</h1>
    
    <!-- 基础组件测试 -->
    <div class="section">
      <h2>基础组件</h2>
      <el-button type="primary">主要按钮</el-button>
      <el-button type="success">成功按钮</el-button>
      <el-button type="info">信息按钮</el-button>
      <el-button type="warning">警告按钮</el-button>
      <el-button type="danger">危险按钮</el-button>
    </div>

    <!-- 表单组件测试 -->
    <div class="section">
      <h2>表单组件</h2>
      <el-form :model="form" label-width="120px">
        <el-form-item label="用户名">
          <el-input v-model="form.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="form.password" type="password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="性别">
          <el-select v-model="form.gender" placeholder="请选择性别">
            <el-option label="男" value="male"></el-option>
            <el-option label="女" value="female"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="爱好">
          <el-checkbox-group v-model="form.hobbies">
            <el-checkbox label="reading" value="reading">阅读</el-checkbox>
            <el-checkbox label="music" value="music">音乐</el-checkbox>
            <el-checkbox label="sports" value="sports">运动</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">提交</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 图标测试 -->
    <div class="section">
      <h2>图标组件</h2>
      <el-button :icon="Search" circle></el-button>
      <el-button :icon="Edit" circle></el-button>
      <el-button :icon="Check" circle></el-button>
      <el-button :icon="Message" circle></el-button>
      <el-button :icon="Star" circle></el-button>
      <el-button :icon="Delete" circle></el-button>
    </div>

    <!-- 消息组件测试 -->
    <div class="section">
      <h2>消息组件</h2>
      <el-button @click="showMessage">显示消息</el-button>
      <el-button @click="showNotification">显示通知</el-button>
      <el-button @click="showAlert">显示警告</el-button>
    </div>

    <!-- 数据展示组件 -->
    <div class="section">
      <h2>数据展示</h2>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="date" label="日期" width="180"></el-table-column>
        <el-table-column prop="name" label="姓名" width="180"></el-table-column>
        <el-table-column prop="address" label="地址"></el-table-column>
      </el-table>
    </div>
  </div>
</template>


<style scoped>
.element-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.section h2 {
  margin-bottom: 20px;
  color: #303133;
}

.el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.el-form {
  max-width: 600px;
}
</style>
