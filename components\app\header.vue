<template>
  <header class="app-header">
    <!-- 主导航栏 -->
    <el-container>
      <el-header class="header-content">
        <!-- Logo 和品牌名 -->
        <div class="brand">
          <el-avatar
            :size="40"
            :src="logoUrl"
            @error="handleAvatarError"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="brand-name">{{ brandName }}</span>
        </div>

        <!-- 主导航菜单 -->
        <el-menu
          :default-active="activeIndex"
          class="main-menu"
          mode="horizontal"
          @select="handleSelect"
          :ellipsis="false"
        >
          <el-menu-item index="/">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          <el-sub-menu index="products">
            <template #title>
              <el-icon><Grid /></el-icon>
              <span>产品</span>
            </template>
            <el-menu-item index="/products/web">Web 应用</el-menu-item>
            <el-menu-item index="/products/mobile">移动应用</el-menu-item>
            <el-menu-item index="/products/desktop">桌面应用</el-menu-item>
          </el-sub-menu>
          <el-menu-item index="/services">
            <el-icon><Tools /></el-icon>
            <span>服务</span>
          </el-menu-item>
          <el-menu-item index="/about">
            <el-icon><InfoFilled /></el-icon>
            <span>关于我们</span>
          </el-menu-item>
          <el-menu-item index="/contact">
            <el-icon><Message /></el-icon>
            <span>联系我们</span>
          </el-menu-item>
        </el-menu>

        <!-- 右侧操作区 -->
        <div class="header-actions">
          <!-- 搜索框 -->
          <el-input
            v-model="searchText"
            class="search-input"
            placeholder="搜索..."
            :prefix-icon="Search"
            @keyup.enter="handleSearch"
            clearable
          />

          <!-- 通知铃铛 -->
          <el-badge :value="notificationCount" class="notification-badge">
            <el-button
              :icon="Bell"
              circle
              @click="showNotifications"
              :class="{ 'has-notifications': notificationCount > 0 }"
            />
          </el-badge>

          <!-- 主题切换 -->
          <el-switch
            v-model="isDark"
            class="theme-switch"
            :active-icon="Moon"
            :inactive-icon="Sunny"
            @change="toggleTheme"
          />

          <!-- 语言切换 -->
          <el-dropdown @command="changeLanguage">
            <el-button circle>
              <el-icon><Setting /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="zh-CN">中文</el-dropdown-item>
                <el-dropdown-item command="en-US">English</el-dropdown-item>
                <el-dropdown-item command="ja-JP">日本語</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 用户菜单 -->
          <el-dropdown class="user-dropdown" @command="handleUserCommand">
            <div class="user-info">
              <el-avatar :size="32" :src="userAvatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ username }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
    </el-container>

    <!-- 移动端菜单抽屉 -->
    <el-drawer
      v-model="mobileMenuVisible"
      title="导航菜单"
      direction="ltr"
      size="280px"
    >
      <el-menu
        :default-active="activeIndex"
        @select="handleMobileSelect"
      >
        <el-menu-item index="/">
          <el-icon><House /></el-icon>
          <span>首页</span>
        </el-menu-item>
        <el-sub-menu index="products">
          <template #title>
            <el-icon><Grid /></el-icon>
            <span>产品</span>
          </template>
          <el-menu-item index="/products/web">Web 应用</el-menu-item>
          <el-menu-item index="/products/mobile">移动应用</el-menu-item>
          <el-menu-item index="/products/desktop">桌面应用</el-menu-item>
        </el-sub-menu>
        <el-menu-item index="/services">
          <el-icon><Tools /></el-icon>
          <span>服务</span>
        </el-menu-item>
        <el-menu-item index="/about">
          <el-icon><InfoFilled /></el-icon>
          <span>关于我们</span>
        </el-menu-item>
        <el-menu-item index="/contact">
          <el-icon><Message /></el-icon>
          <span>联系我们</span>
        </el-menu-item>
      </el-menu>
    </el-drawer>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  User,
  House,
  Grid,
  Tools,
  InfoFilled,
  Message,
  Search,
  Bell,
  Moon,
  Sunny,
  ArrowDown,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'

// 路由相关
const router = useRouter()
const route = useRoute()

// 响应式数据
const searchText = ref('')
const notificationCount = ref(5)
const isDark = ref(false)
const mobileMenuVisible = ref(false)

// 品牌信息
const brandName = ref('我的应用')
const logoUrl = ref('/logo.png')

// 用户信息
const username = ref('张三')
const userAvatar = ref('')

// 当前激活的菜单项
const activeIndex = computed(() => {
  return route.path
})

// 方法定义
const handleAvatarError = () => {
  console.log('头像加载失败')
}

const handleSelect = (index: string) => {
  if (index.startsWith('/')) {
    router.push(index)
  }
}

const handleMobileSelect = (index: string) => {
  mobileMenuVisible.value = false
  if (index.startsWith('/')) {
    router.push(index)
  }
}

const handleSearch = () => {
  if (searchText.value.trim()) {
    ElMessage.success(`搜索: ${searchText.value}`)
    // 这里可以添加实际的搜索逻辑
  }
}

const showNotifications = () => {
  ElNotification({
    title: '通知',
    message: `您有 ${notificationCount.value} 条未读通知`,
    type: 'info'
  })
}

const toggleTheme = (value: string | number | boolean) => {
  const isDarkMode = Boolean(value)
  if (isDarkMode) {
    document.documentElement.classList.add('dark')
    ElMessage.success('已切换到深色主题')
  } else {
    document.documentElement.classList.remove('dark')
    ElMessage.success('已切换到浅色主题')
  }
}

const changeLanguage = (lang: string) => {
  ElMessage.success(`语言已切换为: ${lang}`)
  // 这里可以添加实际的语言切换逻辑
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('已退出登录')
        // 这里可以添加实际的退出登录逻辑
      }).catch(() => {
        ElMessage.info('已取消退出')
      })
      break
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 检查系统主题偏好
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    isDark.value = true
    document.documentElement.classList.add('dark')
  }
})
</script>

<style scoped>
.app-header {
  /* flex:1; */
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
  max-width: 1200px;
  margin: 0 auto;
}

.brand {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.brand-name {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.main-menu {
  flex: 1;
  margin: 0 40px;
  border-bottom: none;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.search-input {
  width: 200px;
}

.notification-badge {
  position: relative;
}

.has-notifications {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

.theme-switch {
  margin: 0 8px;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: var(--el-fill-color-light);
}

.username {
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.dropdown-icon {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  transition: transform 0.3s;
}

.user-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .main-menu {
    display: none;
  }

  .search-input {
    width: 150px;
  }

  .username {
    display: none;
  }
}

@media (max-width: 480px) {
  .brand-name {
    display: none;
  }

  .search-input {
    width: 120px;
  }

  .header-actions {
    gap: 8px;
  }
}

/* 深色主题适配 */
.dark .app-header {
  background: var(--el-bg-color);
  border-bottom-color: var(--el-border-color);
}
</style>