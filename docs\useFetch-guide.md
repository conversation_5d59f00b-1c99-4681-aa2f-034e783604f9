# Nuxt 3 useFetch 完整使用指南

## 📖 目录

- [什么是 useFetch？](#什么是-usefetch)
- [基本语法](#基本语法)
- [类型定义](#类型定义)
- [返回值详解](#返回值详解)
- [核心特性](#核心特性)
- [配置选项](#配置选项)
- [基础用法示例](#基础用法示例)
- [高级用法](#高级用法)
- [状态管理](#状态管理)
- [错误处理](#错误处理)
- [性能优化](#性能优化)
- [与其他方法对比](#与其他方法对比)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)
- [实战案例](#实战案例)

## 什么是 useFetch？

`useFetch` 是 Nuxt 3 提供的核心数据获取组合式函数（Composable），它是 `$fetch` 的高级包装器，专门为服务端渲染（SSR）和客户端水合（Hydration）优化设计。

### 🎯 主要优势

- **防止双重获取**：确保数据只在服务端获取一次，客户端直接使用
- **自动缓存**：智能缓存机制，避免重复请求
- **响应式更新**：依赖变化时自动重新获取数据
- **类型安全**：完整的 TypeScript 支持
- **错误处理**：内置错误状态管理
- **SSR 友好**：完美支持服务端渲染

## 基本语法

```typescript
const { data, error, status, refresh, clear } = await useFetch(url, options)
```

### 参数说明

- `url`: 请求地址，支持字符串、Ref 或函数
- `options`: 可选配置对象

## 类型定义

```typescript
function useFetch<DataT, ErrorT>(
  url: string | Request | Ref<string | Request> | (() => string | Request),
  options?: UseFetchOptions<DataT>
): Promise<AsyncData<DataT, ErrorT>>

interface UseFetchOptions<DataT> {
  key?: MaybeRefOrGetter<string>
  method?: string
  query?: SearchParams
  params?: SearchParams
  body?: RequestInit['body'] | Record<string, any>
  headers?: Record<string, string> | [key: string, value: string][] | Headers
  baseURL?: string
  server?: boolean
  lazy?: boolean
  immediate?: boolean
  getCachedData?: (key: string, nuxtApp: NuxtApp, ctx: AsyncDataRequestContext) => DataT | undefined
  deep?: boolean
  dedupe?: 'cancel' | 'defer'
  default?: () => DataT
  transform?: (input: DataT) => DataT | Promise<DataT>
  pick?: string[]
  $fetch?: typeof globalThis.$fetch
  watch?: MultiWatchSources | false
  onRequest?: (ctx: FetchContext) => void
  onRequestError?: (ctx: FetchContext & { error: Error }) => void
  onResponse?: (ctx: FetchContext & { response: Response }) => void
  onResponseError?: (ctx: FetchContext & { response: Response }) => void
}

interface AsyncData<DataT, ErrorT> {
  data: Ref<DataT | undefined>
  refresh: (opts?: AsyncDataExecuteOptions) => Promise<void>
  execute: (opts?: AsyncDataExecuteOptions) => Promise<void>
  clear: () => void
  error: Ref<ErrorT | undefined>
  status: Ref<AsyncDataRequestStatus>
}

type AsyncDataRequestStatus = 'idle' | 'pending' | 'success' | 'error'
```

## 返回值详解

### data
- **类型**: `Ref<DataT | undefined>`
- **描述**: 获取的数据，响应式引用
- **初始值**: `undefined`（或 `default()` 的返回值）

### error
- **类型**: `Ref<ErrorT | undefined>`
- **描述**: 错误信息，响应式引用
- **初始值**: `undefined`

### status
- **类型**: `Ref<'idle' | 'pending' | 'success' | 'error'>`
- **描述**: 请求状态
- **状态说明**:
  - `idle`: 未开始请求（如设置了 `immediate: false`）
  - `pending`: 请求进行中
  - `success`: 请求成功
  - `error`: 请求失败

### refresh / execute
- **类型**: `(opts?: AsyncDataExecuteOptions) => Promise<void>`
- **描述**: 手动刷新数据，`execute` 是 `refresh` 的别名
- **参数**: 可选的执行选项

### clear
- **类型**: `() => void`
- **描述**: 清除数据和错误状态，重置为初始状态

## 核心特性

### 🔄 防止双重获取

```vue
<script setup>
// ✅ 正确：数据只在服务端获取一次
const { data } = await useFetch('/api/posts')

// ❌ 错误：会在服务端和客户端都获取
const posts = await $fetch('/api/posts')
</script>
```

### 🎯 自动缓存机制

```vue
<script setup>
// 使用 URL 作为默认缓存键
const { data: posts1 } = await useFetch('/api/posts')

// 自定义缓存键
const { data: posts2 } = await useFetch('/api/posts', {
  key: 'my-posts-key'
})

// 相同键的请求会共享缓存
const { data: posts3 } = await useFetch('/api/posts', {
  key: 'my-posts-key' // 与 posts2 共享缓存
})
</script>
```

### ⚡ 响应式更新

```vue
<script setup>
const userId = ref(1)

// 当 userId 变化时自动重新获取
const { data: user } = await useFetch(() => `/api/users/${userId.value}`)

// 或者使用 watch 选项
const { data: posts } = await useFetch('/api/posts', {
  query: { userId },
  watch: [userId]
})
</script>
```

## 配置选项

### 基础请求选项

```typescript
const { data } = await useFetch('/api/posts', {
  // HTTP 方法
  method: 'POST',
  
  // 查询参数（会附加到 URL）
  query: { 
    page: 1, 
    limit: 10,
    search: 'nuxt'
  },
  
  // 路径参数（用于 URL 模板）
  params: { 
    id: 123 
  },
  
  // 请求体
  body: {
    title: 'New Post',
    content: 'Post content'
  },
  
  // 请求头
  headers: {
    'Authorization': 'Bearer token',
    'Content-Type': 'application/json'
  },
  
  // 基础 URL
  baseURL: 'https://api.example.com'
})
```

### 行为控制选项

```typescript
const { data } = await useFetch('/api/posts', {
  // 缓存键（用于数据共享和缓存）
  key: 'posts-list',
  
  // 延迟加载，不阻塞页面导航
  lazy: true,
  
  // 仅在客户端执行
  server: false,
  
  // 不立即执行（需要手动调用 execute）
  immediate: false,
  
  // 深度响应式（默认为 false）
  deep: true,
  
  // 重复请求处理策略
  dedupe: 'cancel' // 或 'defer'
})
```

### 数据处理选项

```typescript
const { data } = await useFetch('/api/posts', {
  // 默认值函数
  default: () => [],
  
  // 数据转换函数
  transform: (data) => {
    return data.map(post => ({
      ...post,
      formattedDate: new Date(post.createdAt).toLocaleDateString()
    }))
  },
  
  // 选择特定字段（减少传输数据）
  pick: ['id', 'title', 'excerpt'],
  
  // 自定义缓存数据获取函数
  getCachedData: (key, nuxtApp, ctx) => {
    return nuxtApp.payload.data[key] || nuxtApp.static.data[key]
  }
})
```

### 监听选项

```typescript
const searchQuery = ref('')
const category = ref('all')

const { data } = await useFetch('/api/posts', {
  query: { 
    search: searchQuery,
    category: category
  },
  
  // 监听依赖变化
  watch: [searchQuery, category],
  
  // 或者禁用自动监听
  watch: false
})
```

## 基础用法示例

### 简单 GET 请求

```vue
<script setup>
const { data: posts, error, status } = await useFetch('/api/posts')
</script>

<template>
  <div>
    <div v-if="status === 'pending'">加载中...</div>
    <div v-else-if="error">错误: {{ error.message }}</div>
    <div v-else>
      <article v-for="post in posts" :key="post.id">
        <h2>{{ post.title }}</h2>
        <p>{{ post.excerpt }}</p>
      </article>
    </div>
  </div>
</template>
```

### 带参数的请求

```vue
<script setup>
const route = useRoute()
const { id } = route.params

const { data: post } = await useFetch(`/api/posts/${id}`, {
  // 当 id 不存在时的默认值
  default: () => null
})

// 动态设置页面标题
useSeoMeta({
  title: () => post.value?.title || '文章详情',
  description: () => post.value?.excerpt || '文章详情页面'
})
</script>

<template>
  <article v-if="post">
    <h1>{{ post.title }}</h1>
    <div v-html="post.content"></div>
  </article>
  <div v-else>
    文章不存在
  </div>
</template>
```

### POST 请求示例

```vue
<script setup>
const formData = ref({
  title: '',
  content: ''
})

const { data: result, execute: submitPost, status } = await useFetch('/api/posts', {
  method: 'POST',
  body: formData,
  immediate: false // 不立即执行
})

const handleSubmit = async () => {
  await submitPost()
  if (result.value) {
    // 提交成功，跳转到文章页面
    await navigateTo(`/posts/${result.value.id}`)
  }
}
</script>

<template>
  <form @submit.prevent="handleSubmit">
    <input v-model="formData.title" placeholder="标题" required>
    <textarea v-model="formData.content" placeholder="内容" required></textarea>
    <button type="submit" :disabled="status === 'pending'">
      {{ status === 'pending' ? '提交中...' : '提交' }}
    </button>
  </form>
</template>
```

## 高级用法

### 请求和响应拦截器

```vue
<script setup>
const { data, error } = await useFetch('/api/auth/login', {
  method: 'POST',
  body: { username: 'user', password: 'pass' },

  // 请求拦截器
  onRequest({ request, options }) {
    // 添加认证头
    const token = useCookie('auth-token')
    if (token.value) {
      options.headers.set('Authorization', `Bearer ${token.value}`)
    }

    // 添加请求时间戳
    options.headers.set('X-Request-Time', Date.now().toString())

    console.log('发送请求:', request)
  },

  // 请求错误拦截器
  onRequestError({ request, options, error }) {
    console.error('请求失败:', error)
    // 可以在这里处理网络错误
  },

  // 响应拦截器
  onResponse({ request, response, options }) {
    // 处理成功响应
    console.log('响应成功:', response.status)

    // 保存认证令牌
    if (response._data?.token) {
      const token = useCookie('auth-token')
      token.value = response._data.token
    }
  },

  // 响应错误拦截器
  onResponseError({ request, response, options }) {
    console.error('响应错误:', response.status)

    // 处理特定错误状态
    if (response.status === 401) {
      // 清除认证信息并跳转到登录页
      const token = useCookie('auth-token')
      token.value = null
      navigateTo('/login')
    } else if (response.status === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足'
      })
    }
  }
})
</script>
```

### 条件请求

```vue
<script setup>
const user = useUser() // 假设这是一个用户状态组合函数
const shouldFetch = computed(() => !!user.value?.id)

// 只有当用户已登录时才获取数据
const { data: userPosts } = await useFetch('/api/user/posts', {
  // 使用计算属性控制是否执行请求
  server: shouldFetch,
  lazy: true,

  // 或者使用函数形式的 URL
  // url: () => shouldFetch.value ? '/api/user/posts' : null,

  // 监听用户状态变化
  watch: [shouldFetch]
})
</script>
```

### 分页数据获取

```vue
<script setup>
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')

const { data: postsData, refresh, status } = await useFetch('/api/posts', {
  query: {
    page: currentPage,
    limit: pageSize,
    search: searchQuery
  },

  // 当分页参数变化时自动重新获取
  watch: [currentPage, pageSize, searchQuery],

  // 数据转换
  transform: (data) => ({
    posts: data.posts || [],
    total: data.total || 0,
    totalPages: Math.ceil((data.total || 0) / pageSize.value)
  }),

  // 默认值
  default: () => ({
    posts: [],
    total: 0,
    totalPages: 0
  })
})

// 分页操作函数
const goToPage = (page) => {
  currentPage.value = page
}

const nextPage = () => {
  if (currentPage.value < postsData.value.totalPages) {
    currentPage.value++
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

// 搜索防抖
const debouncedSearch = useDebounceFn((query) => {
  searchQuery.value = query
  currentPage.value = 1 // 搜索时重置到第一页
}, 300)
</script>

<template>
  <div>
    <!-- 搜索框 -->
    <input
      @input="debouncedSearch($event.target.value)"
      placeholder="搜索文章..."
    >

    <!-- 加载状态 -->
    <div v-if="status === 'pending'" class="loading">
      加载中...
    </div>

    <!-- 文章列表 -->
    <div v-else>
      <article v-for="post in postsData.posts" :key="post.id">
        <h2>{{ post.title }}</h2>
        <p>{{ post.excerpt }}</p>
      </article>

      <!-- 分页控件 -->
      <div class="pagination">
        <button
          @click="prevPage"
          :disabled="currentPage === 1"
        >
          上一页
        </button>

        <span>
          第 {{ currentPage }} 页，共 {{ postsData.totalPages }} 页
        </span>

        <button
          @click="nextPage"
          :disabled="currentPage === postsData.totalPages"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>
```

### 并发请求

```vue
<script setup>
const userId = ref(1)

// 方法 1: 使用 Promise.all 在单个 useAsyncData 中
const { data: userData } = await useAsyncData(
  `user-${userId.value}`,
  async () => {
    const [profile, posts, followers] = await Promise.all([
      $fetch(`/api/users/${userId.value}/profile`),
      $fetch(`/api/users/${userId.value}/posts`),
      $fetch(`/api/users/${userId.value}/followers`)
    ])

    return {
      profile,
      posts,
      followers
    }
  },
  {
    watch: [userId]
  }
)

// 方法 2: 分别获取（会并行执行）
const [
  { data: profile },
  { data: posts },
  { data: followers }
] = await Promise.all([
  useFetch(`/api/users/${userId.value}/profile`),
  useFetch(`/api/users/${userId.value}/posts`),
  useFetch(`/api/users/${userId.value}/followers`)
])
</script>
```

### 无限滚动

```vue
<script setup>
const posts = ref([])
const currentPage = ref(1)
const hasMore = ref(true)
const isLoading = ref(false)

const { execute: loadMore } = await useFetch('/api/posts', {
  query: {
    page: currentPage,
    limit: 10
  },
  immediate: false,

  onResponse({ response }) {
    const newPosts = response._data.posts
    const total = response._data.total

    // 追加新数据
    posts.value.push(...newPosts)

    // 检查是否还有更多数据
    hasMore.value = posts.value.length < total

    // 准备下一页
    currentPage.value++
    isLoading.value = false
  }
})

// 初始加载
await loadMore()

// 加载更多函数
const loadMorePosts = async () => {
  if (isLoading.value || !hasMore.value) return

  isLoading.value = true
  await loadMore()
}

// 滚动监听
const { arrivedState } = useScroll(window)
watch(() => arrivedState.bottom, (isBottom) => {
  if (isBottom) {
    loadMorePosts()
  }
})
</script>

<template>
  <div>
    <article v-for="post in posts" :key="post.id">
      <h2>{{ post.title }}</h2>
      <p>{{ post.excerpt }}</p>
    </article>

    <div v-if="isLoading" class="loading">
      加载更多...
    </div>

    <div v-else-if="!hasMore" class="no-more">
      没有更多内容了
    </div>
  </div>
</template>
```

### 自定义 $fetch 实例

```vue
<!-- plugins/api.client.js -->
<script>
export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig()

  // 创建自定义 $fetch 实例
  const api = $fetch.create({
    baseURL: config.public.apiBase,

    // 默认选项
    retry: 3,
    retryDelay: 500,
    timeout: 10000,

    // 请求拦截器
    onRequest({ request, options }) {
      // 添加认证头
      const token = useCookie('auth-token')
      if (token.value) {
        options.headers.Authorization = `Bearer ${token.value}`
      }

      // 添加 API 版本
      options.headers['API-Version'] = 'v1'
    },

    // 响应拦截器
    onResponse({ response }) {
      // 统一处理响应格式
      if (response._data?.code !== 200) {
        throw createError({
          statusCode: response._data?.code || 500,
          statusMessage: response._data?.message || '请求失败'
        })
      }

      // 返回实际数据
      response._data = response._data.data
    },

    // 错误处理
    onResponseError({ response }) {
      if (response.status === 401) {
        // 清除认证信息
        const token = useCookie('auth-token')
        token.value = null

        // 跳转到登录页
        navigateTo('/login')
      }
    }
  })

  return {
    provide: {
      api
    }
  }
})
</script>
```

```vue
<!-- 使用自定义 API 实例 -->
<script setup>
const { $api } = useNuxtApp()

// 使用自定义实例
const { data: posts } = await useFetch('/posts', {
  $fetch: $api
})

// 或者创建一个组合函数
const useAPI = (url, options = {}) => {
  return useFetch(url, {
    ...options,
    $fetch: $api
  })
}

// 使用组合函数
const { data: users } = await useAPI('/users')
</script>
```

## 状态管理

### 全局状态共享

```vue
<!-- composables/usePostsStore.js -->
<script>
export const usePostsStore = () => {
  // 使用固定的 key 确保全局共享
  const { data: posts, refresh, error, status } = useFetch('/api/posts', {
    key: 'global-posts',
    default: () => []
  })

  const addPost = async (newPost) => {
    // 乐观更新
    posts.value.unshift(newPost)

    try {
      const { data: createdPost } = await useFetch('/api/posts', {
        method: 'POST',
        body: newPost
      })

      // 更新为服务器返回的数据
      posts.value[0] = createdPost
    } catch (error) {
      // 回滚乐观更新
      posts.value.shift()
      throw error
    }
  }

  const deletePost = async (postId) => {
    const originalPosts = [...posts.value]

    // 乐观删除
    posts.value = posts.value.filter(post => post.id !== postId)

    try {
      await useFetch(`/api/posts/${postId}`, {
        method: 'DELETE'
      })
    } catch (error) {
      // 回滚删除
      posts.value = originalPosts
      throw error
    }
  }

  return {
    posts: readonly(posts),
    refresh,
    error: readonly(error),
    status: readonly(status),
    addPost,
    deletePost
  }
}
</script>
```

### 使用全局状态

```vue
<script setup>
// 在任何组件中使用
const { posts, addPost, deletePost, refresh } = usePostsStore()

const handleAddPost = async (postData) => {
  try {
    await addPost(postData)
    // 成功添加
  } catch (error) {
    // 处理错误
    console.error('添加文章失败:', error)
  }
}
</script>
```

## 错误处理

### 基础错误处理

```vue
<script setup>
const { data, error, status, refresh } = await useFetch('/api/posts')

// 检查错误类型
const isNetworkError = computed(() => {
  return error.value && !error.value.statusCode
})

const isServerError = computed(() => {
  return error.value && error.value.statusCode >= 500
})

const isClientError = computed(() => {
  return error.value && error.value.statusCode >= 400 && error.value.statusCode < 500
})
</script>

<template>
  <div>
    <div v-if="status === 'pending'">
      加载中...
    </div>

    <div v-else-if="error">
      <!-- 网络错误 -->
      <div v-if="isNetworkError" class="error-network">
        <h3>网络连接失败</h3>
        <p>请检查您的网络连接</p>
        <button @click="refresh">重试</button>
      </div>

      <!-- 服务器错误 -->
      <div v-else-if="isServerError" class="error-server">
        <h3>服务器错误 ({{ error.statusCode }})</h3>
        <p>{{ error.statusMessage || '服务器暂时不可用' }}</p>
        <button @click="refresh">重试</button>
      </div>

      <!-- 客户端错误 -->
      <div v-else-if="isClientError" class="error-client">
        <h3>请求错误 ({{ error.statusCode }})</h3>
        <p>{{ error.statusMessage || '请求参数有误' }}</p>
        <button @click="$router.go(-1)">返回</button>
      </div>

      <!-- 其他错误 -->
      <div v-else class="error-unknown">
        <h3>未知错误</h3>
        <p>{{ error.message || '发生了未知错误' }}</p>
        <button @click="refresh">重试</button>
      </div>
    </div>

    <div v-else>
      <!-- 正常内容 -->
      <div v-for="post in data" :key="post.id">
        {{ post.title }}
      </div>
    </div>
  </div>
</template>
```

### 重试机制

```vue
<script setup>
const maxRetries = 3
const retryCount = ref(0)
const retryDelay = ref(1000) // 1秒

const { data, error, execute, status } = await useFetch('/api/posts', {
  immediate: false,

  onResponseError({ response }) {
    // 对于 5xx 错误自动重试
    if (response.status >= 500 && retryCount.value < maxRetries) {
      setTimeout(() => {
        retryCount.value++
        retryDelay.value *= 2 // 指数退避
        execute()
      }, retryDelay.value)
    }
  }
})

// 手动重试
const manualRetry = () => {
  retryCount.value = 0
  retryDelay.value = 1000
  execute()
}

// 初始请求
await execute()
</script>

<template>
  <div>
    <div v-if="status === 'pending'">
      加载中...
      <div v-if="retryCount > 0">
        (重试第 {{ retryCount }} 次)
      </div>
    </div>

    <div v-else-if="error">
      <p>加载失败: {{ error.message }}</p>
      <button @click="manualRetry">重试</button>
    </div>

    <div v-else>
      <!-- 成功内容 -->
    </div>
  </div>
</template>
```

### 全局错误处理

```vue
<!-- plugins/error-handler.client.js -->
<script>
export default defineNuxtPlugin(() => {
  // 全局错误处理
  const handleGlobalError = (error, instance, info) => {
    console.error('全局错误:', error)

    // 发送错误报告到监控服务
    if (process.client) {
      // 发送到错误监控服务
      // sendErrorReport(error, info)
    }
  }

  // 注册全局错误处理器
  if (process.client) {
    window.addEventListener('unhandledrejection', (event) => {
      handleGlobalError(event.reason, null, 'unhandledrejection')
    })
  }
})
</script>
```

## 性能优化

### 1. 减少传输数据

```vue
<script setup>
// ✅ 使用 pick 只获取需要的字段
const { data: posts } = await useFetch('/api/posts', {
  pick: ['id', 'title', 'excerpt', 'createdAt']
})

// ✅ 使用 transform 在服务端处理数据
const { data: processedPosts } = await useFetch('/api/posts', {
  transform: (posts) => {
    return posts.map(post => ({
      ...post,
      formattedDate: new Date(post.createdAt).toLocaleDateString(),
      wordCount: post.content.split(' ').length
    }))
  }
})
</script>
```

### 2. 智能缓存策略

```vue
<script setup>
// ✅ 使用有意义的缓存键
const userId = ref(1)
const { data: userProfile } = await useFetch(`/api/users/${userId.value}`, {
  key: `user-profile-${userId.value}`,

  // 自定义缓存数据获取
  getCachedData: (key, nuxtApp) => {
    // 优先从内存缓存获取
    const cached = nuxtApp.payload.data[key]
    if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
      return cached.data
    }
    return undefined
  }
})

// ✅ 条件缓存
const { data: sensitiveData } = await useFetch('/api/sensitive', {
  server: false, // 敏感数据不在服务端缓存
  key: false     // 不缓存敏感数据
})
</script>
```

### 3. 延迟加载策略

```vue
<script setup>
// ✅ 非关键数据使用 lazy 加载
const { data: criticalData } = await useFetch('/api/critical')

const { data: nonCriticalData, status } = useFetch('/api/non-critical', {
  lazy: true,
  server: false // 非关键数据可以只在客户端加载
})
</script>

<template>
  <div>
    <!-- 关键内容立即显示 -->
    <main>
      <h1>{{ criticalData.title }}</h1>
      <p>{{ criticalData.content }}</p>
    </main>

    <!-- 非关键内容延迟显示 -->
    <aside>
      <div v-if="status === 'pending'">
        加载推荐内容...
      </div>
      <div v-else-if="nonCriticalData">
        <h2>推荐阅读</h2>
        <ul>
          <li v-for="item in nonCriticalData" :key="item.id">
            {{ item.title }}
          </li>
        </ul>
      </div>
    </aside>
  </div>
</template>
```

### 4. 请求去重

```vue
<script setup>
// ✅ 使用相同的 key 自动去重
const fetchUserData = (userId) => {
  return useFetch(`/api/users/${userId}`, {
    key: `user-${userId}`,
    dedupe: 'cancel' // 取消之前的请求
  })
}

// 多个组件调用相同的数据，只会发送一次请求
const { data: user1 } = await fetchUserData(1)
const { data: user2 } = await fetchUserData(1) // 复用 user1 的结果
</script>
```

## 与其他方法对比

### useFetch vs $fetch

| 特性 | useFetch | $fetch |
|------|----------|--------|
| **SSR 支持** | ✅ 完整支持 | ❌ 需要手动处理 |
| **自动缓存** | ✅ 内置缓存 | ❌ 无缓存 |
| **响应式** | ✅ 响应式数据 | ❌ 普通 Promise |
| **错误处理** | ✅ 内置状态管理 | ❌ 需要手动处理 |
| **防重复请求** | ✅ 自动去重 | ❌ 可能重复 |
| **使用场景** | 页面数据获取 | 事件驱动请求 |

```vue
<script setup>
// ✅ useFetch - 适用于页面数据
const { data: posts } = await useFetch('/api/posts')

// ✅ $fetch - 适用于用户交互
const handleSubmit = async () => {
  const result = await $fetch('/api/posts', {
    method: 'POST',
    body: formData.value
  })
}
</script>
```

### useFetch vs useAsyncData

| 特性 | useFetch | useAsyncData |
|------|----------|--------------|
| **简洁性** | ✅ 更简洁 | ❌ 需要更多配置 |
| **灵活性** | ❌ 相对固定 | ✅ 更灵活 |
| **自定义处理** | ❌ 有限 | ✅ 完全自定义 |
| **URL 处理** | ✅ 内置 URL 处理 | ❌ 需要手动处理 |

```vue
<script setup>
// useFetch - 简单直接
const { data: posts } = await useFetch('/api/posts')

// useAsyncData - 更灵活的控制
const { data: processedPosts } = await useAsyncData('posts', async () => {
  const posts = await $fetch('/api/posts')
  const users = await $fetch('/api/users')

  // 复杂的数据处理逻辑
  return posts.map(post => ({
    ...post,
    author: users.find(user => user.id === post.authorId)
  }))
})
</script>
```

### useFetch vs useLazyFetch

| 特性 | useFetch | useLazyFetch |
|------|----------|--------------|
| **阻塞导航** | ✅ 阻塞（确保数据可用） | ❌ 不阻塞 |
| **初始加载** | ✅ 立即可用 | ❌ 可能为空 |
| **用户体验** | ✅ 内容完整 | ✅ 响应更快 |
| **适用场景** | 关键数据 | 非关键数据 |

```vue
<script setup>
// 关键数据 - 使用 useFetch
const { data: article } = await useFetch(`/api/articles/${route.params.id}`)

// 非关键数据 - 使用 useLazyFetch
const { data: comments, status } = useLazyFetch(`/api/articles/${route.params.id}/comments`)
</script>

<template>
  <div>
    <!-- 文章内容立即可用 -->
    <article>
      <h1>{{ article.title }}</h1>
      <div v-html="article.content"></div>
    </article>

    <!-- 评论可能需要加载 -->
    <section>
      <h2>评论</h2>
      <div v-if="status === 'pending'">
        加载评论中...
      </div>
      <div v-else-if="comments">
        <div v-for="comment in comments" :key="comment.id">
          {{ comment.content }}
        </div>
      </div>
    </section>
  </div>
</template>
```

## 最佳实践

### ✅ 推荐做法

#### 1. 合理使用缓存键

```vue
<script setup>
// ✅ 使用有意义的缓存键
const userId = ref(1)
const { data: user } = await useFetch(`/api/users/${userId.value}`, {
  key: `user-${userId.value}`
})

// ✅ 动态缓存键
const { data: posts } = await useFetch('/api/posts', {
  key: () => `posts-${currentPage.value}-${searchQuery.value}`,
  query: {
    page: currentPage,
    search: searchQuery
  }
})
</script>
```

#### 2. 错误边界处理

```vue
<script setup>
const { data, error } = await useFetch('/api/posts')

// ✅ 处理特定错误
if (error.value) {
  if (error.value.statusCode === 404) {
    throw createError({
      statusCode: 404,
      statusMessage: '页面不存在'
    })
  }
}
</script>
```

#### 3. 类型安全

```typescript
interface Post {
  id: number
  title: string
  content: string
  createdAt: string
}

interface ApiResponse<T> {
  data: T
  total: number
  page: number
}

// ✅ 使用类型注解
const { data: posts } = await useFetch<ApiResponse<Post[]>>('/api/posts')

// ✅ 类型安全的访问
const firstPost = computed(() => posts.value?.data[0])
```

#### 4. 环境配置

```vue
<script setup>
const config = useRuntimeConfig()

// ✅ 使用环境配置
const { data } = await useFetch('/api/posts', {
  baseURL: config.public.apiBase,
  headers: {
    'API-Key': config.apiKey // 私有配置
  }
})
</script>
```

### ❌ 避免的做法

#### 1. 在循环中使用 useFetch

```vue
<script setup>
const postIds = [1, 2, 3, 4, 5]

// ❌ 错误：会发送多个请求
// const posts = []
// for (const id of postIds) {
//   const { data } = await useFetch(`/api/posts/${id}`)
//   posts.push(data.value)
// }

// ✅ 正确：批量获取
const { data: posts } = await useFetch('/api/posts', {
  query: { ids: postIds.join(',') }
})
</script>
```

#### 2. 忽略错误处理

```vue
<script setup>
// ❌ 错误：没有错误处理
// const { data } = await useFetch('/api/posts')

// ✅ 正确：处理错误
const { data, error } = await useFetch('/api/posts')

if (error.value) {
  console.error('获取数据失败:', error.value)
  // 处理错误逻辑
}
</script>
```

#### 3. 过度使用 server: false

```vue
<script setup>
// ❌ 错误：SEO 重要数据不应该跳过 SSR
// const { data: article } = await useFetch('/api/articles/1', {
//   server: false
// })

// ✅ 正确：SEO 数据应该在服务端获取
const { data: article } = await useFetch('/api/articles/1')

// ✅ 正确：非 SEO 数据可以跳过 SSR
const { data: userPreferences } = await useFetch('/api/user/preferences', {
  server: false
})
</script>
```

## 常见问题

### Q1: 为什么数据在客户端是 null？

**A**: 可能的原因：

1. **服务端渲染失败**
```vue
<script setup>
// 检查服务端是否正确获取数据
const { data, error, status } = await useFetch('/api/posts')

console.log('Status:', status.value)
console.log('Error:', error.value)
</script>
```

2. **API 路径错误**
```vue
<script setup>
// 确保 API 路径正确
const { data } = await useFetch('/api/posts') // 确保这个路径存在
</script>
```

3. **使用了 server: false**
```vue
<script setup>
// 如果设置了 server: false，数据只在客户端获取
const { data } = await useFetch('/api/posts', {
  server: false // 移除这行或设置为 true
})
</script>
```

### Q2: 如何处理认证相关的请求？

**A**: 使用拦截器或自定义 $fetch 实例：

```vue
<script setup>
const { data } = await useFetch('/api/protected', {
  onRequest({ options }) {
    const token = useCookie('auth-token')
    if (token.value) {
      options.headers.Authorization = `Bearer ${token.value}`
    }
  },

  onResponseError({ response }) {
    if (response.status === 401) {
      // 重定向到登录页
      navigateTo('/login')
    }
  }
})
</script>
```

### Q3: 如何实现数据的实时更新？

**A**: 结合 WebSocket 或定时刷新：

```vue
<script setup>
const { data: posts, refresh } = await useFetch('/api/posts')

// 方法 1: 定时刷新
const refreshInterval = setInterval(() => {
  refresh()
}, 30000) // 每30秒刷新一次

onUnmounted(() => {
  clearInterval(refreshInterval)
})

// 方法 2: WebSocket 更新
const { $socket } = useNuxtApp()
$socket.on('posts-updated', () => {
  refresh()
})
</script>
```

### Q4: 如何处理大量数据的性能问题？

**A**: 使用分页、虚拟滚动和数据选择：

```vue
<script setup>
// 1. 分页加载
const { data: posts } = await useFetch('/api/posts', {
  query: {
    page: currentPage,
    limit: 20
  }
})

// 2. 只获取需要的字段
const { data: postList } = await useFetch('/api/posts', {
  pick: ['id', 'title', 'excerpt'] // 只获取列表需要的字段
})

// 3. 使用 transform 预处理数据
const { data: processedPosts } = await useFetch('/api/posts', {
  transform: (posts) => {
    // 在服务端处理数据，减少客户端计算
    return posts.map(post => ({
      ...post,
      summary: post.content.substring(0, 100) + '...'
    }))
  }
})
</script>
```

## 实战案例

### 案例1: 博客文章详情页

```vue
<!-- pages/blog/[slug].vue -->
<script setup lang="ts">
interface Post {
  id: number
  title: string
  content: string
  excerpt: string
  slug: string
  author: {
    name: string
    avatar: string
  }
  createdAt: string
  tags: string[]
  readTime: number
}

const route = useRoute()
const { slug } = route.params

// 获取文章数据
const { data: post, error } = await useFetch<Post>(`/api/posts/${slug}`, {
  key: `post-${slug}`,

  // 只获取需要的字段
  pick: ['title', 'content', 'excerpt', 'author', 'createdAt', 'tags', 'readTime'],

  // 数据转换
  transform: (post: Post) => ({
    ...post,
    formattedDate: new Date(post.createdAt).toLocaleDateString('zh-CN'),
    estimatedReadTime: `约 ${post.readTime} 分钟阅读`
  })
})

// 错误处理
if (error.value) {
  throw createError({
    statusCode: error.value.statusCode || 404,
    statusMessage: error.value.statusMessage || '文章不存在'
  })
}

// 动态 SEO
useSeoMeta({
  title: () => post.value?.title,
  description: () => post.value?.excerpt,
  ogTitle: () => post.value?.title,
  ogDescription: () => post.value?.excerpt,
  ogType: 'article',
  articleAuthor: () => post.value?.author.name,
  articlePublishedTime: () => post.value?.createdAt
})

// 结构化数据
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: () => JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: post.value?.title,
        description: post.value?.excerpt,
        author: {
          '@type': 'Person',
          name: post.value?.author.name
        },
        datePublished: post.value?.createdAt
      })
    }
  ]
})

// 相关文章（延迟加载）
const { data: relatedPosts } = useLazyFetch('/api/posts/related', {
  query: {
    slug: slug,
    tags: () => post.value?.tags.join(',')
  },
  server: false // 非关键数据，客户端加载
})
</script>

<template>
  <article class="blog-post">
    <header>
      <h1>{{ post.title }}</h1>
      <div class="meta">
        <img :src="post.author.avatar" :alt="post.author.name">
        <span>{{ post.author.name }}</span>
        <time>{{ post.formattedDate }}</time>
        <span>{{ post.estimatedReadTime }}</span>
      </div>
      <div class="tags">
        <span v-for="tag in post.tags" :key="tag" class="tag">
          {{ tag }}
        </span>
      </div>
    </header>

    <div class="content" v-html="post.content"></div>

    <aside v-if="relatedPosts?.length" class="related">
      <h3>相关文章</h3>
      <ul>
        <li v-for="related in relatedPosts" :key="related.id">
          <NuxtLink :to="`/blog/${related.slug}`">
            {{ related.title }}
          </NuxtLink>
        </li>
      </ul>
    </aside>
  </article>
</template>
```

### 案例2: 用户仪表板

```vue
<!-- pages/dashboard.vue -->
<script setup lang="ts">
// 认证检查
definePageMeta({
  middleware: 'auth'
})

const user = useUser()

// 并发获取仪表板数据
const [
  { data: stats },
  { data: recentActivity },
  { data: notifications }
] = await Promise.all([
  // 统计数据 - 关键数据，服务端获取
  useFetch('/api/dashboard/stats', {
    key: `dashboard-stats-${user.value.id}`,
    transform: (data) => ({
      ...data,
      growthRate: ((data.thisMonth - data.lastMonth) / data.lastMonth * 100).toFixed(1)
    })
  }),

  // 最近活动 - 延迟加载
  useLazyFetch('/api/dashboard/activity', {
    key: `dashboard-activity-${user.value.id}`,
    default: () => []
  }),

  // 通知 - 客户端获取
  useFetch('/api/dashboard/notifications', {
    key: `dashboard-notifications-${user.value.id}`,
    server: false,
    default: () => []
  })
])

// 实时数据更新
const { refresh: refreshStats } = stats
const refreshInterval = setInterval(refreshStats, 60000) // 每分钟更新统计

onUnmounted(() => {
  clearInterval(refreshInterval)
})

// 标记通知为已读
const markAsRead = async (notificationId: number) => {
  try {
    await $fetch(`/api/notifications/${notificationId}/read`, {
      method: 'POST'
    })

    // 乐观更新
    if (notifications.value) {
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.read = true
      }
    }
  } catch (error) {
    console.error('标记通知失败:', error)
  }
}
</script>

<template>
  <div class="dashboard">
    <h1>仪表板</h1>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <h3>总收入</h3>
        <p class="value">¥{{ stats?.totalRevenue?.toLocaleString() }}</p>
        <p class="growth" :class="{ positive: stats?.growthRate > 0 }">
          {{ stats?.growthRate }}%
        </p>
      </div>

      <div class="stat-card">
        <h3>新用户</h3>
        <p class="value">{{ stats?.newUsers }}</p>
      </div>

      <div class="stat-card">
        <h3>订单数</h3>
        <p class="value">{{ stats?.orders }}</p>
      </div>
    </div>

    <!-- 最近活动 -->
    <section class="recent-activity">
      <h2>最近活动</h2>
      <div v-if="!recentActivity">
        <div class="skeleton" v-for="i in 5" :key="i"></div>
      </div>
      <ul v-else>
        <li v-for="activity in recentActivity" :key="activity.id">
          <span class="time">{{ activity.time }}</span>
          <span class="action">{{ activity.description }}</span>
        </li>
      </ul>
    </section>

    <!-- 通知 -->
    <section class="notifications">
      <h2>通知</h2>
      <div v-if="notifications?.length">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="notification"
          :class="{ unread: !notification.read }"
        >
          <p>{{ notification.message }}</p>
          <button
            v-if="!notification.read"
            @click="markAsRead(notification.id)"
          >
            标记为已读
          </button>
        </div>
      </div>
      <p v-else>暂无通知</p>
    </section>
  </div>
</template>
```

### 案例3: 电商产品列表

```vue
<!-- pages/products/index.vue -->
<script setup lang="ts">
interface Product {
  id: number
  name: string
  price: number
  image: string
  category: string
  rating: number
  inStock: boolean
}

interface ProductFilters {
  category: string
  minPrice: number
  maxPrice: number
  sortBy: 'price' | 'rating' | 'name'
  sortOrder: 'asc' | 'desc'
}

// 响应式过滤器
const filters = reactive<ProductFilters>({
  category: '',
  minPrice: 0,
  maxPrice: 10000,
  sortBy: 'name',
  sortOrder: 'asc'
})

const currentPage = ref(1)
const pageSize = 12

// 获取产品数据
const { data: productsData, refresh, status } = await useFetch('/api/products', {
  query: {
    page: currentPage,
    limit: pageSize,
    category: () => filters.category || undefined,
    minPrice: () => filters.minPrice || undefined,
    maxPrice: () => filters.maxPrice || undefined,
    sortBy: () => filters.sortBy,
    sortOrder: () => filters.sortOrder
  },

  // 监听过滤器变化
  watch: [
    () => filters.category,
    () => filters.minPrice,
    () => filters.maxPrice,
    () => filters.sortBy,
    () => filters.sortOrder,
    currentPage
  ],

  // 数据转换
  transform: (data) => ({
    products: data.products || [],
    total: data.total || 0,
    totalPages: Math.ceil((data.total || 0) / pageSize),
    hasMore: data.products?.length === pageSize
  }),

  default: () => ({
    products: [],
    total: 0,
    totalPages: 0,
    hasMore: false
  })
})

// 获取分类列表
const { data: categories } = await useFetch('/api/categories', {
  key: 'product-categories',
  default: () => []
})

// 过滤器变化时重置页码
watch([
  () => filters.category,
  () => filters.minPrice,
  () => filters.maxPrice,
  () => filters.sortBy,
  () => filters.sortOrder
], () => {
  currentPage.value = 1
})

// 防抖搜索
const searchQuery = ref('')
const debouncedSearch = useDebounceFn((query: string) => {
  // 实现搜索逻辑
  refresh()
}, 300)

// SEO
useSeoMeta({
  title: '产品列表',
  description: '浏览我们的产品目录，找到您需要的商品'
})
</script>

<template>
  <div class="products-page">
    <h1>产品列表</h1>

    <!-- 搜索和过滤器 -->
    <div class="filters">
      <input
        v-model="searchQuery"
        @input="debouncedSearch($event.target.value)"
        placeholder="搜索产品..."
        class="search-input"
      >

      <select v-model="filters.category">
        <option value="">所有分类</option>
        <option v-for="category in categories" :key="category.id" :value="category.slug">
          {{ category.name }}
        </option>
      </select>

      <div class="price-range">
        <input v-model.number="filters.minPrice" type="number" placeholder="最低价格">
        <input v-model.number="filters.maxPrice" type="number" placeholder="最高价格">
      </div>

      <select v-model="filters.sortBy">
        <option value="name">按名称</option>
        <option value="price">按价格</option>
        <option value="rating">按评分</option>
      </select>

      <select v-model="filters.sortOrder">
        <option value="asc">升序</option>
        <option value="desc">降序</option>
      </select>
    </div>

    <!-- 加载状态 -->
    <div v-if="status === 'pending'" class="loading">
      <div class="skeleton-grid">
        <div class="skeleton-card" v-for="i in 12" :key="i"></div>
      </div>
    </div>

    <!-- 产品网格 -->
    <div v-else-if="productsData.products.length" class="products-grid">
      <div v-for="product in productsData.products" :key="product.id" class="product-card">
        <NuxtLink :to="`/products/${product.id}`">
          <img :src="product.image" :alt="product.name" loading="lazy">
          <h3>{{ product.name }}</h3>
          <p class="price">¥{{ product.price.toLocaleString() }}</p>
          <div class="rating">
            <span>⭐ {{ product.rating }}</span>
            <span v-if="!product.inStock" class="out-of-stock">缺货</span>
          </div>
        </NuxtLink>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <p>没有找到符合条件的产品</p>
    </div>

    <!-- 分页 -->
    <div v-if="productsData.totalPages > 1" class="pagination">
      <button
        @click="currentPage--"
        :disabled="currentPage === 1"
      >
        上一页
      </button>

      <span>第 {{ currentPage }} 页，共 {{ productsData.totalPages }} 页</span>

      <button
        @click="currentPage++"
        :disabled="currentPage === productsData.totalPages"
      >
        下一页
      </button>
    </div>
  </div>
</template>
```

## 总结

`useFetch` 是 Nuxt 3 中最重要的数据获取工具，它提供了：

- **🚀 性能优化**: 防止双重获取，智能缓存
- **🔄 响应式**: 自动响应依赖变化
- **🛡️ 类型安全**: 完整的 TypeScript 支持
- **⚡ 开发体验**: 简洁的 API，强大的功能
- **🎯 SSR 友好**: 完美支持服务端渲染

掌握 `useFetch` 的正确使用方法，能够帮助你构建高性能、用户体验良好的 Nuxt 应用。记住要根据具体场景选择合适的配置选项，合理处理错误，并注意性能优化。

---

**相关资源**:
- [Nuxt 3 官方文档](https://nuxt.com/docs/api/composables/use-fetch)
- [ofetch 文档](https://github.com/unjs/ofetch)
- [Vue 3 响应式 API](https://vuejs.org/api/reactivity-core.html)
