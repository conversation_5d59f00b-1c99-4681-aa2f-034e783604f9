# Nuxt.js 页面元数据设置指南

## 🚫 为什么不能在异步操作后调用 `definePageMeta`

### 编译时 vs 运行时

`definePageMeta` 是一个**编译时宏**，这意味着：

1. **编译时执行**：在构建过程中就需要确定值
2. **静态分析**：Nuxt 需要在编译时分析和提取元数据
3. **路由生成**：用于生成路由配置和页面结构
4. **不支持异步**：无法等待异步操作完成

```javascript
// ❌ 错误：这会导致 "await in definePageMeta" 错误
const { data: post } = await useFetch('/api/posts/1')
definePageMeta({
  title: post.value?.title  // 编译时无法获取异步数据
})
```

## ✅ 正确的替代方案

### 1. 使用 `definePageMeta` 设置静态元数据

```javascript
// ✅ 正确：使用静态值或路由参数
const route = useRoute()
definePageMeta({
  title: `文章详情 - ${route.params.slug}`,
  description: '文章详情页面',
  layout: 'article'
})
```

### 2. 使用 `useSeoMeta` 处理动态 SEO 数据

```javascript
// 异步获取数据
const { data: post } = await useFetch('/api/posts/1')

// ✅ 使用 useSeoMeta 设置动态 SEO 信息
useSeoMeta({
  title: () => post.value?.title || '默认标题',
  description: () => post.value?.excerpt || '默认描述',
  ogTitle: () => post.value?.title,
  ogDescription: () => post.value?.excerpt,
  ogImage: () => post.value?.image,
  twitterCard: 'summary_large_image'
})
```

### 3. 使用 `useHead` 处理复杂头部信息

```javascript
useHead({
  title: () => post.value?.title || '默认标题',
  meta: [
    {
      name: 'description',
      content: () => post.value?.excerpt || '默认描述'
    },
    {
      name: 'keywords',
      content: () => post.value?.tags?.join(', ') || ''
    }
  ],
  link: [
    {
      rel: 'canonical',
      href: () => `https://example.com/posts/${post.value?.slug}`
    }
  ],
  script: [
    {
      type: 'application/ld+json',
      innerHTML: () => JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: post.value?.title,
        description: post.value?.excerpt
      })
    }
  ]
})
```

## 🔄 响应式更新

使用函数形式可以确保元数据随数据变化而更新：

```javascript
// ✅ 响应式：当 post 数据更新时，标题也会更新
useSeoMeta({
  title: () => post.value?.title || '加载中...'
})

// ❌ 非响应式：只会使用初始值
useSeoMeta({
  title: post.value?.title || '加载中...'
})
```

## 📋 最佳实践总结

1. **`definePageMeta`**：用于静态的页面级配置
   - 布局选择
   - 路由级别的配置
   - 静态的页面信息

2. **`useSeoMeta`**：用于动态的 SEO 信息
   - 页面标题和描述
   - Open Graph 标签
   - Twitter Card 信息

3. **`useHead`**：用于复杂的头部管理
   - 自定义 meta 标签
   - 链接和脚本
   - 结构化数据

4. **使用函数形式**：确保响应式更新
   - `() => computedValue`
   - 自动响应数据变化
