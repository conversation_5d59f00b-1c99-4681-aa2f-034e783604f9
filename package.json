{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "clean": "rm -rf .nuxt .output dist"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.5.0", "@vueuse/nuxt": "^13.5.0", "element-plus": "^2.10.4", "nuxt": "^4.0.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "devDependencies": {"@element-plus/nuxt": "^1.1.4", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0"}}