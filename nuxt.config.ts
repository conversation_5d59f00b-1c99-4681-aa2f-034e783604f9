// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: {
    enabled: true,
    timeline: {
      enabled: true,
    }
  },

  // 模块配置 - Element Plus 自动导入
  modules: [
    '@element-plus/nuxt'
  ],

  app: {
    baseURL:  process.env.APP_BASE_URL || '/',
  },

  devServer:{
    port: Number(process.env.SERVER_PORT) || 3000,
  },

  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅在服务端可用）
    secretKey: process.env.SECRET_KEY || 'secret-key',

    // 公共配置（客户端和服务端都可用）
    public: {
      apiBase: process.env.PUBLIC_API_BASE || 'http://localhost:3000/api',
    }
  },
  // routeRules:{
  //   '/request':{
  //     // redirect: '/new-request'
  //     // proxy: '/new-request'
  //   }
  // },

  nitro:{
    storage:{
      // 文件系统存储 - 数据会保存到磁盘
      db:{
        driver: 'fs',
        base: './storage/data' // 存储目录
      },

      // 内存存储 - 用于临时缓存
      cache: {
        driver: 'memory'
      }
    }
  }


})
