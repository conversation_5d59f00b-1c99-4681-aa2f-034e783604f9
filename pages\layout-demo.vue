<template>
  <div class="layout-demo">
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h2>Header 和 Footer 组件演示</h2>
          <el-tag type="success">Element Plus</el-tag>
        </div>
      </template>
      
      <div class="demo-content">
        <el-alert
          title="组件说明"
          type="info"
          :closable="false"
          show-icon
        >
          <p>本页面展示了使用 Element Plus 组件构建的 Header 和 Footer 组件。</p>
          <p>这些组件包含了丰富的功能和交互效果。</p>
        </el-alert>

        <el-divider content-position="left">Header 组件功能</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="hover">
              <h3>导航功能</h3>
              <ul>
                <li>响应式导航菜单</li>
                <li>品牌 Logo 和名称</li>
                <li>多级菜单支持</li>
                <li>移动端抽屉菜单</li>
              </ul>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <h3>交互功能</h3>
              <ul>
                <li>搜索框</li>
                <li>通知提醒</li>
                <li>主题切换</li>
                <li>语言切换</li>
                <li>用户菜单</li>
              </ul>
            </el-card>
          </el-col>
        </el-row>

        <el-divider content-position="left">Footer 组件功能</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="hover">
              <h3>公司信息</h3>
              <ul>
                <li>公司 Logo 和介绍</li>
                <li>社交媒体链接</li>
                <li>品牌展示</li>
              </ul>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <h3>导航链接</h3>
              <ul>
                <li>快速链接</li>
                <li>产品服务</li>
                <li>帮助中心</li>
              </ul>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <h3>联系方式</h3>
              <ul>
                <li>联系信息</li>
                <li>邮件订阅</li>
                <li>回到顶部</li>
              </ul>
            </el-card>
          </el-col>
        </el-row>

        <el-divider content-position="left">测试功能</el-divider>
        
        <div class="test-buttons">
          <el-button type="primary" @click="testHeaderSearch">测试搜索功能</el-button>
          <el-button type="success" @click="testNotification">测试通知功能</el-button>
          <el-button type="warning" @click="testThemeToggle">切换主题</el-button>
          <el-button type="info" @click="testFooterSubscribe">测试订阅功能</el-button>
        </div>

        <el-divider content-position="left">技术特性</el-divider>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="UI 框架">Element Plus</el-descriptions-item>
          <el-descriptions-item label="前端框架">Vue 3 + Nuxt</el-descriptions-item>
          <el-descriptions-item label="响应式设计">支持移动端适配</el-descriptions-item>
          <el-descriptions-item label="主题支持">浅色/深色主题</el-descriptions-item>
          <el-descriptions-item label="图标库">Element Plus Icons</el-descriptions-item>
          <el-descriptions-item label="自动导入">无需手动导入组件</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: 'Layout Demo - Header & Footer 组件演示'
})

// 测试方法
const testHeaderSearch = () => {
  ElMessage({
    message: '请在页面顶部的搜索框中输入内容并按回车键测试搜索功能',
    type: 'info',
    duration: 3000
  })
}

const testNotification = () => {
  ElMessage({
    message: '请点击页面顶部的通知铃铛图标测试通知功能',
    type: 'info',
    duration: 3000
  })
}

const testThemeToggle = () => {
  ElMessage({
    message: '请点击页面顶部的主题切换开关测试深色/浅色主题切换',
    type: 'info',
    duration: 3000
  })
}

const testFooterSubscribe = () => {
  ElMessage({
    message: '请滚动到页面底部，在"订阅我们的通讯"部分输入邮箱测试订阅功能',
    type: 'info',
    duration: 4000
  })
}
</script>

<style scoped>
.layout-demo {
  max-width: 1200px;
  margin: 0 auto;
}

.demo-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.demo-content {
  padding: 20px 0;
}

.demo-content ul {
  margin: 0;
  padding-left: 20px;
}

.demo-content li {
  margin-bottom: 8px;
  color: var(--el-text-color-regular);
}

.test-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin: 20px 0;
}

.el-card h3 {
  margin-top: 0;
  color: var(--el-text-color-primary);
}

@media (max-width: 768px) {
  .test-buttons {
    flex-direction: column;
  }
  
  .test-buttons .el-button {
    width: 100%;
  }
}
</style>
