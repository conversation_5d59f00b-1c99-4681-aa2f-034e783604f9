# Element Plus 自动导入配置指南

## 概述

本项目已成功配置 Element Plus 的自动导入功能，无需手动导入组件即可直接使用。

## 已安装的依赖

```json
{
  "dependencies": {
    "element-plus": "^2.10.4",
    "@element-plus/icons-vue": "^2.3.1"
  },
  "devDependencies": {
    "@element-plus/nuxt": "^1.1.4",
    "unplugin-auto-import": "^19.3.0",
    "unplugin-vue-components": "^28.8.0"
  }
}
```

## 配置文件

### nuxt.config.ts

```typescript
export default defineNuxtConfig({
  // 模块配置 - Element Plus 自动导入
  modules: [
    '@element-plus/nuxt'
  ],
  // ... 其他配置
})
```

## 使用方式

### 1. 组件自动导入

所有 Element Plus 组件都可以直接使用，无需手动导入：

```vue
<template>
  <div>
    <!-- 直接使用，无需导入 -->
    <el-button type="primary">主要按钮</el-button>
    <el-input v-model="value" placeholder="请输入内容"></el-input>
    <el-select v-model="selected">
      <el-option label="选项1" value="1"></el-option>
      <el-option label="选项2" value="2"></el-option>
    </el-select>
  </div>
</template>

<script setup>
// 无需导入组件，直接使用
const value = ref('')
const selected = ref('')
</script>
```

### 2. 图标使用

图标需要手动导入：

```vue
<template>
  <div>
    <el-button :icon="Search">搜索</el-button>
    <el-button :icon="Edit">编辑</el-button>
  </div>
</template>

<script setup>
import { Search, Edit } from '@element-plus/icons-vue'
</script>
```

### 3. 消息组件使用

消息类组件（ElMessage、ElNotification、ElMessageBox）会自动全局注册：

```vue
<script setup>
// 直接使用，无需导入
const showMessage = () => {
  ElMessage.success('操作成功！')
}

const showNotification = () => {
  ElNotification({
    title: '提示',
    message: '这是一条通知消息',
    type: 'info'
  })
}

const showAlert = () => {
  ElMessageBox.alert('这是内容', '标题')
}
</script>
```

## 支持的功能

### ✅ 已自动导入的内容

- 所有 Element Plus 组件（el-button、el-input、el-table 等）
- 消息类组件的全局方法（ElMessage、ElNotification、ElMessageBox）
- 组件的 TypeScript 类型定义

### ⚠️ 需要手动导入的内容

- 图标组件（需要从 `@element-plus/icons-vue` 导入）
- 特定的工具函数或常量

## 测试页面

访问 `/element-test` 页面可以查看各种组件的使用示例和测试效果。

## 常见问题

### Q: 组件没有样式？
A: 确保 `@element-plus/nuxt` 模块已正确安装并在 `nuxt.config.ts` 中配置。

### Q: TypeScript 类型提示不工作？
A: 确保项目中有正确的 TypeScript 配置，Element Plus 的类型会自动注册。

### Q: 图标不显示？
A: 图标需要手动导入，确保从 `@element-plus/icons-vue` 正确导入所需图标。

## 自定义主题

如需自定义主题，可以创建 SCSS 变量文件：

```scss
// assets/scss/element-variables.scss
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #409eff,
    ),
  ),
);
```

然后在 `nuxt.config.ts` 中引入：

```typescript
export default defineNuxtConfig({
  css: [
    '~/assets/scss/element-variables.scss'
  ],
  // ...
})
```

## Header 和 Footer 组件

项目中已经创建了功能丰富的 Header 和 Footer 组件，使用了大量 Element Plus 组件。

### Header 组件功能

- **导航菜单**: 响应式水平菜单，支持多级菜单
- **品牌展示**: Logo 和公司名称
- **搜索功能**: 带图标的搜索输入框
- **通知系统**: 带徽章的通知按钮
- **主题切换**: 深色/浅色主题切换开关
- **语言切换**: 多语言下拉菜单
- **用户菜单**: 用户头像和下拉菜单
- **移动端适配**: 抽屉式移动端菜单

### Footer 组件功能

- **公司信息**: 公司 Logo、名称和描述
- **社交媒体**: 微信、微博、GitHub 等社交链接
- **快速导航**: 分类导航链接
- **联系信息**: 地址、电话、邮箱等联系方式
- **邮件订阅**: 通讯订阅功能
- **版权信息**: 版权声明和法律链接
- **回到顶部**: 浮动回到顶部按钮

### 使用方式

组件已经在 `layouts/default.vue` 中引用：

```vue
<template>
  <div class="app-layout">
    <AppHeader />
    <main class="main-content">
      <slot />
    </main>
    <AppFooter />
  </div>
</template>
```

### 演示页面

访问 `/layout-demo` 页面可以查看组件的详细功能说明和测试方法。

### 自定义配置

可以通过修改组件内的响应式数据来自定义内容：

**Header 组件**:
- `brandName`: 品牌名称
- `logoUrl`: Logo 图片地址
- `username`: 用户名
- `notificationCount`: 通知数量

**Footer 组件**:
- `companyName`: 公司名称
- `companyDescription`: 公司描述
- `address`: 公司地址
- `phone`: 联系电话
- `email`: 联系邮箱

## 更多信息

- [Element Plus 官方文档](https://element-plus.org/)
- [Element Plus Nuxt 模块文档](https://github.com/element-plus/element-plus-nuxt)
