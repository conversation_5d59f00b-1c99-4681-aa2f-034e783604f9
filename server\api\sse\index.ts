export default defineEventHandler(async (event) => {
  console.log('=== 文件系统存储测试 ===')

  // 使用配置的文件系统存储
  const store = useStorage('db')

  console.log('设置 key1 = value1...')
  await store.setItem('key1', 'value1')
  console.log('设置完成')

  console.log('获取 key1...')
  const value = await store.getItem('key1')
  console.log('获取到的值:', value)

  // 获取所有键
  const allKeys = await store.getKeys()
  console.log('所有键:', allKeys)

  console.log('=== 存储测试完成 ===');

    const eStream = createEventStream(event)

    const interval = setInterval(async () => {
      await eStream.push('Message @ ' + new Date().toLocaleTimeString())
    }, 1000)

    eStream.onClosed(async()=>{
      clearInterval(interval)
      await eStream.push('Closed')
      await eStream.close();
    })

    return eStream.send()
})