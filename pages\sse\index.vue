<script setup lang="ts">
// import {useStorage} from 'unstorage'

let eStream: EventSource | null = null
const messages = ref<string[]>([])
const isConnected = ref(false)
const connectionError = ref<string | null>(null)

const connectSSE = () => {
	if (eStream) return // 防止重复连接

	eStream = new EventSource('/api/sse')

	eStream.onopen = () => {
		console.log('SSE 连接已建立')
		isConnected.value = true
		connectionError.value = null
	}

	eStream.onmessage = (event) => {
		console.log(
			'%c [ event ]-6',
			'font-size:13px; background:#6bb1f7; color:#aff5ff;',
			event
		)
		messages.value.push(event.data)

		// 限制消息数量，避免内存泄漏
		if (messages.value.length > 50) {
			messages.value = messages.value.slice(-50)
		}
	}

	eStream.onerror = (error) => {
		console.error('SSE 连接错误:', error)
		isConnected.value = false
		connectionError.value = 'SSE 连接出现错误'
	}
}

const onClosed = () => {
	if (eStream) {
		eStream.close()
		eStream = null
		isConnected.value = false
	}
}

const reconnect = () => {
	onClosed()
	setTimeout(connectSSE, 1000)
}

const clearMessages = () => {
	messages.value = []
}

if (import.meta.client) {
	connectSSE()
}

	// const re = useStorage('root')
	// console.log('%c [ re ]-61', 'font-size:13px; background:#b0935c; color:#f4d7a0;', re);

// 组件卸载时清理连接
onUnmounted(() => {
	onClosed()
})
</script>

<template>
	<div class="sse-demo">
		<h1>Server-Sent Events 演示</h1>

		<!-- 连接状态 -->
		<div class="status-bar">
			<div class="status">
				<span v-if="isConnected" class="status-connected">● 已连接</span>
				<span v-else class="status-disconnected">● 未连接</span>
				<span v-if="connectionError" class="error">{{ connectionError }}</span>
			</div>

			<!-- 控制按钮 -->
			<div class="controls">
				<el-button type="danger" @click="onClosed" :disabled="!isConnected">
					断开连接
				</el-button>

				<el-button type="primary" @click="reconnect" :disabled="isConnected">
					重新连接
				</el-button>

				<el-button
					type="warning"
					@click="clearMessages"
					:disabled="messages.length === 0"
				>
					清空消息
				</el-button>
			</div>
		</div>

		<!-- 消息列表 -->
		<div class="messages-container">
			<h3>接收到的消息 ({{ messages.length }})</h3>
			<div class="messages-list">
				<div
					v-for="(message, index) in messages"
					:key="index"
					class="message-item"
				>
					<span class="message-index">#{{ index + 1 }}</span>
					<span class="message-content">{{ message }}</span>
				</div>
				<div v-if="messages.length === 0" class="no-messages">暂无消息</div>
			</div>
		</div>

	</div>
</template>

<style scoped>
.sse-demo {
	padding: 20px;
	max-width: 800px;
	margin: 0 auto;
}

.status-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	background: #f5f5f5;
	border-radius: 8px;
	margin-bottom: 20px;
}

.status {
	display: flex;
	align-items: center;
	gap: 10px;
}

.status-connected {
	color: #67c23a;
	font-weight: bold;
}

.status-disconnected {
	color: #f56c6c;
	font-weight: bold;
}

.error {
	color: #e6a23c;
	font-size: 14px;
}

.controls {
	display: flex;
	gap: 10px;
}

.messages-container {
	background: white;
	border: 1px solid #dcdfe6;
	border-radius: 8px;
	overflow: hidden;
}

.messages-container h3 {
	margin: 0;
	padding: 15px;
	background: #f8f9fa;
	border-bottom: 1px solid #dcdfe6;
}

.messages-list {
	max-height: 400px;
	overflow-y: auto;
	padding: 10px;
}

.message-item {
	display: flex;
	align-items: center;
	padding: 8px 0;
	border-bottom: 1px solid #f0f0f0;
}

.message-item:last-child {
	border-bottom: none;
}

.message-index {
	color: #909399;
	font-size: 12px;
	min-width: 40px;
	margin-right: 10px;
}

.message-content {
	flex: 1;
	font-family: monospace;
	font-size: 14px;
}

.no-messages {
	text-align: center;
	color: #909399;
	padding: 40px;
	font-style: italic;
}
</style>
