<template>
  <footer class="app-footer">
    <el-container>

      <!-- 底部版权信息 -->
      <el-divider />
      <div class="footer-bottom">
        <div class="copyright">
          <span>&copy; {{ currentYear }} {{ companyName }}. 保留所有权利.</span>
        </div>
        <div class="footer-links">
          <el-link @click="showPrivacyPolicy" :underline="false">隐私政策</el-link>
          <el-divider direction="vertical" />
          <el-link @click="showTermsOfService" :underline="false">服务条款</el-link>
          <el-divider direction="vertical" />
          <el-link @click="showSitemap" :underline="false">网站地图</el-link>
        </div>
        <div class="back-to-top">
          <el-backtop :right="40" :bottom="40">
            <el-button type="primary" :icon="Top" circle />
          </el-backtop>
        </div>
      </div>
    </el-container>
  </footer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  OfficeBuilding,
  ChatDotRound,
  Share,
  Link,
  Message,
  House,
  Grid,
  InfoFilled,
  Phone,
  QuestionFilled,
  Location,
  Clock,
  Top
} from '@element-plus/icons-vue'

// 路由
const router = useRouter()

// 响应式数据
const subscribeEmail = ref('')
const subscribing = ref(false)

// 公司信息
const companyName = ref('我的科技公司')
const companyLogo = ref('/company-logo.png')
const companyDescription = ref('专注于为客户提供优质的技术解决方案和服务')

// 联系信息
const address = ref('北京市朝阳区科技园区123号')
const phone = ref('+86 ************')
const email = ref('<EMAIL>')
const workingHours = ref('周一至周五 9:00-18:00')

// 当前年份
const currentYear = computed(() => new Date().getFullYear())

// 方法定义
const navigateTo = (path: string) => {
  router.push(path)
}

const openSocialLink = (platform: string) => {
  const links = {
    wechat: '#',
    weibo: 'https://weibo.com/mycompany',
    github: 'https://github.com/mycompany',
    email: `mailto:${email.value}`
  }

  if (platform === 'wechat') {
    ElMessage.info('请扫描二维码添加微信')
    // 这里可以显示微信二维码弹窗
  } else {
    window.open(links[platform as keyof typeof links], '_blank')
  }
}

const handleSubscribe = async () => {
  if (!subscribeEmail.value) {
    ElMessage.warning('请输入邮箱地址')
    return
  }

  // 简单的邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(subscribeEmail.value)) {
    ElMessage.error('请输入有效的邮箱地址')
    return
  }

  subscribing.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('订阅成功！感谢您的关注')
    subscribeEmail.value = ''
  } catch (error) {
    ElMessage.error('订阅失败，请稍后重试')
  } finally {
    subscribing.value = false
  }
}

const showPrivacyPolicy = () => {
  ElMessageBox.alert(
    '这里是隐私政策的内容...',
    '隐私政策',
    {
      confirmButtonText: '我知道了',
      type: 'info'
    }
  )
}

const showTermsOfService = () => {
  ElMessageBox.alert(
    '这里是服务条款的内容...',
    '服务条款',
    {
      confirmButtonText: '我知道了',
      type: 'info'
    }
  )
}

const showSitemap = () => {
  router.push('/sitemap')
}
</script>

<style scoped>
.app-footer {
  background: var(--el-bg-color-page);
  border-top: 1px solid var(--el-border-color);
  margin-top: auto;
  display: flex;
  flex-direction: column;
}

:deep(.el-container){
  display: block;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
  padding: 60px 20px 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 16px 0;
  border-bottom: 2px solid var(--el-color-primary);
  padding-bottom: 8px;
  display: inline-block;
}

.company-info {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  margin-bottom: 20px;
}

.company-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.company-desc {
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin: 0;
}

.social-links {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.link-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.link-list li {
  display: flex;
  align-items: center;
}

.link-list .el-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-regular);
  transition: color 0.3s;
}

.link-list .el-link:hover {
  color: var(--el-color-primary);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--el-text-color-regular);
}

.contact-item .el-icon {
  color: var(--el-color-primary);
  font-size: 16px;
}

.contact-item .el-link {
  color: var(--el-text-color-regular);
}

.contact-item .el-link:hover {
  color: var(--el-color-primary);
}

.newsletter {
  margin-top: 24px;
}

.newsletter h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 16px 0;
}

.subscribe-input {
  width: 100%;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  flex-wrap: wrap;
  gap: 16px;
}

.copyright {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-links .el-link {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.footer-links .el-link:hover {
  color: var(--el-color-primary);
}

.back-to-top {
  position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 32px;
    padding: 40px 16px 32px;
  }

  .company-info {
    flex-direction: column;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .footer-content {
    padding: 32px 12px 24px;
  }

  .section-title {
    font-size: 16px;
  }

  .subscribe-input {
    font-size: 14px;
  }
}

/* 深色主题适配 */
.dark .app-footer {
  background: var(--el-bg-color-page);
  border-top-color: var(--el-border-color);
}

/* 动画效果 */
.social-links .el-button {
  transition: transform 0.3s ease;
}

.social-links .el-button:hover {
  transform: translateY(-2px);
}

.link-list .el-link {
  transition: all 0.3s ease;
}

.link-list .el-link:hover {
  transform: translateX(4px);
}
</style>