<script setup lang="ts">
// 设置页面元数据
definePageMeta({
  title: '博客列表',
  description: '查看所有博客文章'
})

// 设置 SEO 信息
useSeoMeta({
  title: '博客列表 - 我的网站',
  description: '浏览我的所有博客文章，包含技术教程和个人分享',
  ogTitle: '博客列表',
  ogDescription: '浏览我的所有博客文章'
})
</script>

<template>
  <div>
    <h1>博客文章列表</h1>
    <div class="blog-list">
      <div class="blog-item">
        <h2>
          <NuxtLink to="/blog/1">Hello World</NuxtLink>
        </h2>
        <p>欢迎来到我的博客</p>
      </div>
      
      <div class="blog-item">
        <h2>
          <NuxtLink to="/blog/2">Nuxt.js 教程</NuxtLink>
        </h2>
        <p>Nuxt.js 是一个基于 Vue.js 的框架</p>
      </div>
      
      <div class="blog-item">
        <h2>
          <NuxtLink to="/blog/hello-world">Hello World (通过 slug)</NuxtLink>
        </h2>
        <p>使用 slug 访问文章</p>
      </div>
      
      <div class="blog-item">
        <h2>
          <NuxtLink to="/blog/nuxt-tutorial">Nuxt.js 教程 (通过 slug)</NuxtLink>
        </h2>
        <p>使用 slug 访问教程</p>
        <UiButton>这是btn</UiButton>
      </div>
    </div>
  </div>
</template>

<style scoped>
.blog-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.blog-item {
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  background: #f8fafc;
}

.blog-item h2 {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
}

.blog-item h2 a {
  color: #3b82f6;
  text-decoration: none;
}

.blog-item h2 a:hover {
  text-decoration: underline;
}

.blog-item p {
  margin: 0;
  color: #64748b;
}
</style>
