# Nuxt 3 中的 EventSource (SSE) 解决方案

## 🚨 问题描述

在 Nuxt 3 中使用 `EventSource` 时遇到 "EventSource is not defined" 错误，这是因为 `EventSource` 是浏览器 Web API，在服务端渲染时不可用。

## 🔧 解决方案

### 方案 1: 使用 onMounted（推荐）

```vue
<script setup lang="ts">
let eStream: EventSource | null = null
const messages = ref<string[]>([])
const isConnected = ref(false)

// 在组件挂载后创建连接
onMounted(() => {
  eStream = new EventSource('/api/sse')
  
  eStream.onopen = () => {
    console.log('SSE 连接已建立')
    isConnected.value = true
  }
  
  eStream.onmessage = (event) => {
    console.log('收到消息:', event.data)
    messages.value.push(event.data)
  }
  
  eStream.onerror = (error) => {
    console.error('SSE 连接错误:', error)
    isConnected.value = false
  }
})

const closeConnection = () => {
  if (eStream) {
    eStream.close()
    eStream = null
    isConnected.value = false
  }
}

// 组件卸载时清理连接
onUnmounted(() => {
  closeConnection()
})
</script>

<template>
  <div>
    <div class="status">
      连接状态: {{ isConnected ? '已连接' : '未连接' }}
    </div>
    
    <el-button 
      type="danger" 
      @click="closeConnection"
      :disabled="!isConnected"
    >
      断开连接
    </el-button>
    
    <div class="messages">
      <h3>接收到的消息:</h3>
      <ul>
        <li v-for="(message, index) in messages" :key="index">
          {{ message }}
        </li>
      </ul>
    </div>
  </div>
</template>
```

### 方案 2: 使用 ClientOnly 组件

```vue
<template>
  <div>
    <ClientOnly>
      <SSEComponent />
      <template #fallback>
        <div>正在加载 SSE 连接...</div>
      </template>
    </ClientOnly>
  </div>
</template>
```

```vue
<!-- components/SSEComponent.vue -->
<script setup lang="ts">
const eStream = new EventSource('/api/sse')
const messages = ref<string[]>([])

eStream.onmessage = (event) => {
  messages.value.push(event.data)
}

onUnmounted(() => {
  eStream.close()
})
</script>

<template>
  <div>
    <div v-for="message in messages" :key="message">
      {{ message }}
    </div>
  </div>
</template>
```

### 方案 3: 使用组合函数（推荐）

```typescript
// composables/useSSE.ts
export const useSSE = (url: string) => {
  const messages = ref<string[]>([])
  const isConnected = ref(false)
  const error = ref<string | null>(null)
  let eventSource: EventSource | null = null

  const connect = () => {
    if (process.server) return

    try {
      eventSource = new EventSource(url)
      
      eventSource.onopen = () => {
        isConnected.value = true
        error.value = null
        console.log('SSE 连接已建立')
      }
      
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          messages.value.push(data)
        } catch {
          messages.value.push(event.data)
        }
      }
      
      eventSource.onerror = (err) => {
        isConnected.value = false
        error.value = 'SSE 连接错误'
        console.error('SSE 错误:', err)
      }
    } catch (err) {
      error.value = '创建 SSE 连接失败'
      console.error('创建 SSE 失败:', err)
    }
  }

  const disconnect = () => {
    if (eventSource) {
      eventSource.close()
      eventSource = null
      isConnected.value = false
    }
  }

  const reconnect = () => {
    disconnect()
    setTimeout(connect, 1000)
  }

  // 自动重连逻辑
  const setupAutoReconnect = () => {
    if (eventSource) {
      eventSource.addEventListener('error', () => {
        if (!isConnected.value) {
          setTimeout(reconnect, 3000) // 3秒后重连
        }
      })
    }
  }

  onMounted(() => {
    connect()
    setupAutoReconnect()
  })

  onUnmounted(() => {
    disconnect()
  })

  return {
    messages: readonly(messages),
    isConnected: readonly(isConnected),
    error: readonly(error),
    connect,
    disconnect,
    reconnect
  }
}
```

使用组合函数：

```vue
<script setup lang="ts">
const { messages, isConnected, error, disconnect, reconnect } = useSSE('/api/sse')
</script>

<template>
  <div>
    <div class="status">
      <span v-if="isConnected" class="connected">● 已连接</span>
      <span v-else class="disconnected">● 未连接</span>
      <span v-if="error" class="error">{{ error }}</span>
    </div>
    
    <div class="controls">
      <button @click="disconnect" :disabled="!isConnected">
        断开连接
      </button>
      <button @click="reconnect">
        重新连接
      </button>
    </div>
    
    <div class="messages">
      <div v-for="(message, index) in messages" :key="index">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.connected { color: green; }
.disconnected { color: red; }
.error { color: orange; }
</style>
```

### 方案 4: 使用 process.client 检查（兼容性方案）

```vue
<script setup lang="ts">
let eStream: EventSource | null = null

// 检查是否在客户端环境
if (process.client && typeof EventSource !== 'undefined') {
  eStream = new EventSource('/api/sse')
  
  eStream.onmessage = (event) => {
    console.log('收到消息:', event.data)
  }
}

const closeConnection = () => {
  if (eStream && process.client) {
    eStream.close()
    eStream = null
  }
}
</script>
```

## 🚀 完整的 SSE 服务端实现

```typescript
// server/api/sse.ts
export default defineEventHandler(async (event) => {
  // 设置 SSE 响应头
  setHeader(event, 'Content-Type', 'text/event-stream')
  setHeader(event, 'Cache-Control', 'no-cache')
  setHeader(event, 'Connection', 'keep-alive')
  setHeader(event, 'Access-Control-Allow-Origin', '*')
  setHeader(event, 'Access-Control-Allow-Headers', 'Cache-Control')

  // 发送初始连接消息
  const sendMessage = (data: any) => {
    return `data: ${JSON.stringify(data)}\n\n`
  }

  // 创建可写流
  const stream = new ReadableStream({
    start(controller) {
      // 发送欢迎消息
      controller.enqueue(sendMessage({ 
        type: 'welcome', 
        message: '连接已建立',
        timestamp: new Date().toISOString()
      }))

      // 定时发送数据
      const interval = setInterval(() => {
        controller.enqueue(sendMessage({
          type: 'data',
          message: `当前时间: ${new Date().toLocaleString()}`,
          timestamp: new Date().toISOString()
        }))
      }, 1000)

      // 清理定时器
      setTimeout(() => {
        clearInterval(interval)
        controller.close()
      }, 30000) // 30秒后关闭连接
    }
  })

  return stream
})
```

## 💡 最佳实践

### 1. 错误处理和重连

```typescript
export const useSSEWithRetry = (url: string, maxRetries = 3) => {
  const retryCount = ref(0)
  const { messages, isConnected, error, connect, disconnect } = useSSE(url)

  const connectWithRetry = async () => {
    try {
      await connect()
      retryCount.value = 0
    } catch (err) {
      if (retryCount.value < maxRetries) {
        retryCount.value++
        setTimeout(connectWithRetry, 2000 * retryCount.value)
      }
    }
  }

  return {
    messages,
    isConnected,
    error,
    retryCount: readonly(retryCount),
    connect: connectWithRetry,
    disconnect
  }
}
```

### 2. 消息类型处理

```typescript
interface SSEMessage {
  type: 'notification' | 'update' | 'error'
  data: any
  timestamp: string
}

export const useTypedSSE = (url: string) => {
  const notifications = ref<SSEMessage[]>([])
  const updates = ref<SSEMessage[]>([])
  const errors = ref<SSEMessage[]>([])

  const { messages } = useSSE(url)

  watch(messages, (newMessages) => {
    newMessages.forEach((msg: SSEMessage) => {
      switch (msg.type) {
        case 'notification':
          notifications.value.push(msg)
          break
        case 'update':
          updates.value.push(msg)
          break
        case 'error':
          errors.value.push(msg)
          break
      }
    })
  })

  return {
    notifications: readonly(notifications),
    updates: readonly(updates),
    errors: readonly(errors)
  }
}
```

## 🔍 常见问题

### Q: 为什么在开发环境中 SSE 连接不稳定？
A: 开发环境中的热重载可能会中断 SSE 连接，这是正常现象。生产环境中会更稳定。

### Q: 如何处理网络断开后的重连？
A: 使用 `navigator.onLine` 监听网络状态，网络恢复后自动重连。

### Q: SSE 连接数量有限制吗？
A: 浏览器对同一域名的 SSE 连接有限制（通常是 6 个），需要合理管理连接。

## 📚 相关资源

- [MDN EventSource 文档](https://developer.mozilla.org/zh-CN/docs/Web/API/EventSource)
- [Server-Sent Events 规范](https://html.spec.whatwg.org/multipage/server-sent-events.html)
- [Nuxt 3 服务端 API](https://nuxt.com/docs/guide/directory-structure/server)
