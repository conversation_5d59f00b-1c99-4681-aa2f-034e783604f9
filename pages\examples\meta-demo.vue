<script setup>
// ❌ 错误示例：这样会报错
// const { data: post } = await useFetch('/api/posts/1')
// definePageMeta({
//   title: post.value?.title  // 错误：不能在异步操作后调用
// })

// ✅ 正确方式 1：使用静态值
definePageMeta({
  title: '元数据演示页面',
  description: '演示如何正确设置页面元数据',
  layout: 'default'
})

// 异步获取数据
const { data: post } = await useFetch('/api/posts/hello-world')

// ✅ 正确方式 2：使用 useSeoMeta 处理动态数据
useSeoMeta({
  title: () => post.value?.title || '默认标题',
  description: () => post.value?.excerpt || '默认描述',
  ogTitle: () => post.value?.title,
  ogDescription: () => post.value?.excerpt,
  ogImage: () => post.value?.image
})

// ✅ 正确方式 3：使用 useHead 处理复杂的头部信息
useHead({
  title: () => post.value?.title || '默认标题',
  meta: [
    {
      name: 'description',
      content: () => post.value?.excerpt || '默认描述'
    },
    {
      property: 'og:title',
      content: () => post.value?.title || '默认标题'
    },
    {
      property: 'og:description', 
      content: () => post.value?.excerpt || '默认描述'
    }
  ],
  link: [
    {
      rel: 'canonical',
      href: () => `https://example.com/posts/${post.value?.slug}`
    }
  ]
})
</script>

<template>
  <div>
    <h1>元数据演示页面</h1>
    <div v-if="post">
      <h2>{{ post.title }}</h2>
      <p>{{ post.content }}</p>
      <p><strong>摘要:</strong> {{ post.excerpt }}</p>
    </div>
    <div v-else>
      <p>加载中...</p>
    </div>
  </div>
</template>
