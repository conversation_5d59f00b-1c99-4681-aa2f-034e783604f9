<script setup lang="ts">
// 设置页面元数据（必须在最前面）
definePageMeta({
	title: '博客文章详情',
	description: '博客文章详情页面',
  layout:'empty',
})

// 获取路由参数
const route = useRoute()
const {id} = route.params

console.log(
	'%c [ slug ]-5',
	'font-size:13px; background:#a79c77; color:#ebe0bb;',
	id
)
const loading = ref(true)
// 获取文章数据
const { data: post } = await useFetch(`/api/posts/${id}`)

loading.value = false

// 动态设置 SEO 头部信息
useSeoMeta({
	title: () => post.value?.title || `博客文章 - ${id}`,
	description: () => post.value?.excerpt || '博客文章详情页面',
	ogTitle: () => post.value?.title,
	ogDescription: () => post.value?.excerpt,
	ogImage: () => post.value?.image,
})

useHead({
  meta:[
    {name:'title' , content: post.value?.title},
    {name:'description' , content: post.value?.content}
  ]
})
</script>

<template>
	<div>
		{{ loading }}
		<h1>{{ post?.title }}</h1>
		<p>{{ post?.content }}</p>
		<p><strong>摘要:</strong> {{ post?.excerpt }}</p>
		<p><strong>作者:</strong> {{ post?.author }}</p>
		<p><strong>发布时间:</strong> {{ post?.createdAt }}</p>
	</div>
</template>
