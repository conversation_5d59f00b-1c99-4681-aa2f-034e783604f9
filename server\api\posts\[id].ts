export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id')

  // 模拟数据库查询
  const posts = {
    '1': {
      id: 1,
      title: 'Hello World',
      slug: 'hello-world',
      content: '这是我的第一篇博客文章！欢迎来到我的博客世界。',
      excerpt: '欢迎来到我的博客',
      image: '/images/hello-world.jpg',
      createdAt: '2024-01-01',
      author: 'Admin'
    },
    '2': {
      id: 2,
      title: 'Nuxt.js 教程',
      slug: 'nuxt-tutorial',
      content: '学习如何使用 Nuxt.js 构建现代 Web 应用程序。Nuxt.js 是一个强大的 Vue.js 框架。',
      excerpt: 'Nuxt.js 是一个基于 Vue.js 的框架',
      image: '/images/nuxt-tutorial.jpg',
      createdAt: '2024-01-02',
      author: 'Admin'
    },
    'hello-world': {
      id: 1,
      title: 'Hello World',
      slug: 'hello-world',
      content: '这是我的第一篇博客文章！欢迎来到我的博客世界。',
      excerpt: '欢迎来到我的博客',
      image: '/images/hello-world.jpg',
      createdAt: '2024-01-01',
      author: 'Admin'
    },
    'nuxt-tutorial': {
      id: 2,
      title: 'Nuxt.js 教程',
      slug: 'nuxt-tutorial',
      content: '学习如何使用 Nuxt.js 构建现代 Web 应用程序。Nuxt.js 是一个强大的 Vue.js 框架。',
      excerpt: 'Nuxt.js 是一个基于 Vue.js 的框架',
      image: '/images/nuxt-tutorial.jpg',
      createdAt: '2024-01-02',
      author: 'Admin'
    }
  }

  const post = posts[id as keyof typeof posts]

  if (!post) {
    throw createError({
      statusCode: 404,
      statusMessage: '文章未找到'
    })
  }

  // 模拟网络延迟
  const res = await new Promise(resolve => setTimeout(()=>{
    resolve(post)
  }, 300))

  return res
})