<script setup lang="ts">
useHead({
	title: "Rex's Nuxt Demo",
	meta: [
		{ name: 'description', content: 'Nuxt 3 demo' },
		{
			name: 'viewport',
			content:
				'width=device-width, initial-scale=1.0, max-scale=1.0, user-scalable=no',
		},
	],
})

// const config = useRuntimeConfig();
// console.log('%c [ config ]-3', 'font-size:13px; background:#5a6a53; color:#9eae97;', config);
</script>

<template>
	<NuxtLayout>
    <NuxtPage />
  </NuxtLayout>
</template>
